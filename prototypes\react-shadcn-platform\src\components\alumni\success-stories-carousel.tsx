import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight, Quote, Briefcase, GraduationCap, MapPin, Calendar } from 'lucide-react';
import { AlumniBadge } from './alumni-badge';

interface SuccessStory {
  id: string;
  name: string;
  batch: number;
  school: string;
  currentRole: string;
  company: string;
  location: string;
  story: string;
  achievement: string;
  photo?: string;
  linkedIn?: string;
  featured?: boolean;
}

interface SuccessStoriesCarouselProps {
  stories: SuccessStory[];
  autoPlay?: boolean;
  interval?: number;
  className?: string;
}

export function SuccessStoriesCarousel({
  stories,
  autoPlay = true,
  interval = 5000,
  className,
}: SuccessStoriesCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (!autoPlay) return;

    const timer = setInterval(() => {
      handleNext();
    }, interval);

    return () => clearInterval(timer);
  }, [currentIndex, autoPlay, interval]);

  const handleNext = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev + 1) % stories.length);
    setTimeout(() => setIsAnimating(false), 300);
  };

  const handlePrev = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev - 1 + stories.length) % stories.length);
    setTimeout(() => setIsAnimating(false), 300);
  };

  const currentStory = stories[currentIndex];
  if (!currentStory) return null;

  return (
    <div className={cn('relative overflow-hidden rounded-xl bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800', className)}>
      {/* Featured Badge */}
      {currentStory.featured && (
        <div className="absolute top-4 right-4 z-10">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg">
            ✨ Featured Alumni
          </span>
        </div>
      )}

      {/* Main Content */}
      <div className="p-8 md:p-12">
        <div className="grid md:grid-cols-2 gap-8 items-center">
          {/* Left: Story Content */}
          <div className="space-y-6">
            {/* Quote Icon */}
            <Quote className="w-10 h-10 text-blue-500 opacity-50" />
            
            {/* Success Story */}
            <div className="space-y-4">
              <h3 className="text-2xl font-bold leading-tight">
                From Campus to {currentStory.achievement}
              </h3>
              <p className="text-lg text-muted-foreground leading-relaxed">
                "{currentStory.story}"
              </p>
            </div>

            {/* Alumni Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white font-bold text-xl">
                  {currentStory.photo ? (
                    <img 
                      src={currentStory.photo} 
                      alt={currentStory.name}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    currentStory.name.split(' ').map(n => n[0]).join('')
                  )}
                </div>
                <div>
                  <h4 className="font-bold text-lg">{currentStory.name}</h4>
                  <AlumniBadge 
                    batch={currentStory.batch} 
                    school={currentStory.school}
                    size="sm"
                  />
                </div>
              </div>

              {/* Current Role */}
              <div className="flex items-start space-x-3 text-sm">
                <Briefcase className="w-4 h-4 text-muted-foreground mt-0.5" />
                <div>
                  <div className="font-semibold">{currentStory.currentRole}</div>
                  <div className="text-muted-foreground">{currentStory.company}</div>
                </div>
              </div>

              {/* Location */}
              <div className="flex items-center space-x-3 text-sm text-muted-foreground">
                <MapPin className="w-4 h-4" />
                <span>{currentStory.location}</span>
              </div>

              {/* LinkedIn */}
              {currentStory.linkedIn && (
                <a 
                  href={currentStory.linkedIn}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center space-x-2 text-sm text-blue-600 hover:text-blue-700 font-medium"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                  </svg>
                  <span>Connect on LinkedIn</span>
                </a>
              )}
            </div>
          </div>

          {/* Right: Visual Element */}
          <div className="relative">
            <div className="relative z-10 bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-600 rounded-xl p-8 text-white shadow-xl">
              {/* Journey Timeline */}
              <div className="space-y-6">
                <h4 className="text-lg font-bold mb-4">Journey Highlights</h4>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                      <GraduationCap className="w-4 h-4" />
                    </div>
                    <div>
                      <div className="font-semibold">Graduated</div>
                      <div className="text-sm opacity-90">Batch of {currentStory.batch}</div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                      <Briefcase className="w-4 h-4" />
                    </div>
                    <div>
                      <div className="font-semibold">Current Position</div>
                      <div className="text-sm opacity-90">{currentStory.currentRole}</div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                      <Calendar className="w-4 h-4" />
                    </div>
                    <div>
                      <div className="font-semibold">Years of Experience</div>
                      <div className="text-sm opacity-90">{new Date().getFullYear() - currentStory.batch} years</div>
                    </div>
                  </div>
                </div>

                {/* Achievement Highlight */}
                <div className="mt-6 pt-6 border-t border-white border-opacity-20">
                  <div className="text-sm uppercase tracking-wider opacity-70 mb-2">Key Achievement</div>
                  <div className="text-lg font-bold">{currentStory.achievement}</div>
                </div>
              </div>
            </div>

            {/* Decorative Elements */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-500 opacity-10 rounded-full blur-xl" />
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-purple-500 opacity-10 rounded-full blur-xl" />
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between mt-8">
          <div className="flex items-center space-x-2">
            {stories.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={cn(
                  'w-2 h-2 rounded-full transition-all',
                  index === currentIndex
                    ? 'w-8 bg-blue-600'
                    : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400'
                )}
                aria-label={`Go to story ${index + 1}`}
              />
            ))}
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={handlePrev}
              className="p-2 rounded-lg bg-white dark:bg-slate-800 border border-border/40 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors"
              aria-label="Previous story"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <button
              onClick={handleNext}
              className="p-2 rounded-lg bg-white dark:bg-slate-800 border border-border/40 hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors"
              aria-label="Next story"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}