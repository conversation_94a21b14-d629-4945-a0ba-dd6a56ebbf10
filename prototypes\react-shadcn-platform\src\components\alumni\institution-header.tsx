import React from 'react';
import { cn } from '@/lib/utils';
import { BrandConfig } from '@/lib/branding/brand-config';
import { Building, Award, Calendar, Users } from 'lucide-react';

interface InstitutionHeaderProps {
  showStats?: boolean;
  variant?: 'default' | 'compact' | 'banner';
  className?: string;
}

export function InstitutionHeader({
  showStats = true,
  variant = 'default',
  className,
}: InstitutionHeaderProps) {
  const currentYear = new Date().getFullYear();
  const yearsOfExcellence = currentYear - BrandConfig.institution.established;

  // Mock stats - in production, these would come from API
  const stats = {
    alumni: '50,000+',
    chapters: BrandConfig.alumni.chapters.length,
    countries: '120+',
    placements: '95%',
  };

  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center space-x-3', className)}>
        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white font-bold">
          {BrandConfig.institution.shortName.substring(0, 2)}
        </div>
        <div>
          <div className="font-semibold">{BrandConfig.institution.name}</div>
          <div className="text-xs text-muted-foreground">{BrandConfig.institution.tagline}</div>
        </div>
      </div>
    );
  }

  if (variant === 'banner') {
    return (
      <div className={cn(
        'relative overflow-hidden rounded-xl',
        'bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600',
        'p-8 text-white',
        className
      )}>
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 0h60v60H0z' fill='none'/%3E%3Cpath d='M30 30l15-15M30 30l15 15M30 30l-15 15M30 30l-15-15' stroke='white' stroke-width='0.5' opacity='0.3'/%3E%3C/svg%3E")`,
            backgroundSize: '60px 60px'
          }} />
        </div>

        <div className="relative">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">{BrandConfig.institution.name}</h1>
              <p className="text-lg opacity-90">{BrandConfig.institution.tagline}</p>
              <div className="flex items-center space-x-4 mt-3">
                <span className="text-sm opacity-80">Est. {BrandConfig.institution.established}</span>
                <span className="text-sm opacity-80">•</span>
                <span className="text-sm opacity-80">{yearsOfExcellence} Years of Excellence</span>
                <span className="text-sm opacity-80">•</span>
                <span className="text-sm opacity-80">{BrandConfig.institution.accreditation}</span>
              </div>
            </div>
            <div className="w-24 h-24 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
              <span className="text-4xl font-bold">{BrandConfig.institution.shortName}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Main Header */}
      <div className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 rounded-xl p-6 border border-slate-200 dark:border-slate-700">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white font-bold text-xl">
              {BrandConfig.institution.shortName}
            </div>
            <div>
              <h2 className="text-2xl font-bold mb-1">{BrandConfig.institution.name}</h2>
              <p className="text-muted-foreground mb-3">{BrandConfig.institution.tagline}</p>
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <span>Established {BrandConfig.institution.established}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Award className="w-4 h-4 text-muted-foreground" />
                  <span>{BrandConfig.institution.accreditation}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold text-blue-600">{yearsOfExcellence}</div>
            <div className="text-sm text-muted-foreground">Years of Excellence</div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      {showStats && (
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-card rounded-lg p-4 border border-border/40">
            <div className="flex items-center space-x-2 mb-2">
              <Users className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-muted-foreground">Alumni Network</span>
            </div>
            <div className="text-2xl font-bold">{stats.alumni}</div>
          </div>
          <div className="bg-card rounded-lg p-4 border border-border/40">
            <div className="flex items-center space-x-2 mb-2">
              <Building className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-muted-foreground">Global Chapters</span>
            </div>
            <div className="text-2xl font-bold">{stats.chapters}</div>
          </div>
          <div className="bg-card rounded-lg p-4 border border-border/40">
            <div className="flex items-center space-x-2 mb-2">
              <Award className="w-5 h-5 text-purple-600" />
              <span className="text-sm font-medium text-muted-foreground">Countries</span>
            </div>
            <div className="text-2xl font-bold">{stats.countries}</div>
          </div>
          <div className="bg-card rounded-lg p-4 border border-border/40">
            <div className="flex items-center space-x-2 mb-2">
              <Award className="w-5 h-5 text-orange-600" />
              <span className="text-sm font-medium text-muted-foreground">Placement Rate</span>
            </div>
            <div className="text-2xl font-bold">{stats.placements}</div>
          </div>
        </div>
      )}
    </div>
  );
}