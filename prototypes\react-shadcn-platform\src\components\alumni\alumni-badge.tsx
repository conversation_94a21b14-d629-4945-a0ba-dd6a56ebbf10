import React from 'react';
import { cn } from '@/lib/utils';
import { BrandConfig } from '@/lib/branding/brand-config';

interface AlumniBadgeProps {
  batch: string | number;
  school?: string;
  achievement?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function AlumniBadge({ 
  batch, 
  school, 
  achievement,
  size = 'md',
  className 
}: AlumniBadgeProps) {
  const year = typeof batch === 'string' ? parseInt(batch) : batch;
  const specialBatch = BrandConfig.alumni.batches.special.find(b => b.year === year);
  const schoolInfo = school ? BrandConfig.academic.schools.find(s => s.id === school) : null;
  const achievementInfo = achievement ? BrandConfig.academic.achievements.find(a => a.id === achievement) : null;

  const sizeClasses = {
    sm: 'text-xs px-2 py-0.5',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-1.5',
  };

  const getBadgeStyle = () => {
    if (specialBatch?.badge === 'gold') {
      return 'bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 text-white shadow-lg';
    }
    if (specialBatch?.badge === 'silver') {
      return 'bg-gradient-to-r from-gray-300 via-gray-400 to-gray-500 text-white shadow-lg';
    }
    if (specialBatch?.badge === 'ruby') {
      return 'bg-gradient-to-r from-red-400 via-red-500 to-red-600 text-white shadow-lg';
    }
    return 'bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 text-white';
  };

  return (
    <div className={cn('inline-flex items-center gap-2', className)}>
      {/* Batch Badge */}
      <span
        className={cn(
          'inline-flex items-center rounded-full font-semibold',
          sizeClasses[size],
          getBadgeStyle(),
          'shadow-sm border border-white/20'
        )}
      >
        <span className="font-bold">
          {BrandConfig.alumni.batches.prefix} '{String(year).slice(-2)}
        </span>
        {specialBatch && (
          <span className="ml-1 opacity-90">• {specialBatch.name}</span>
        )}
      </span>

      {/* School Badge */}
      {schoolInfo && (
        <span
          className={cn(
            'inline-flex items-center rounded-full',
            sizeClasses[size],
            'bg-gradient-to-r from-purple-500 to-purple-600 text-white',
            'shadow-sm border border-white/20'
          )}
        >
          <span className="mr-1">{schoolInfo.icon}</span>
          <span className="font-medium">{schoolInfo.name.split(' ').pop()}</span>
        </span>
      )}

      {/* Achievement Badge */}
      {achievementInfo && (
        <span
          className={cn(
            'inline-flex items-center rounded-full',
            sizeClasses[size],
            achievementInfo.color === 'gold' && 'bg-gradient-to-r from-yellow-400 to-amber-500 text-white',
            achievementInfo.color === 'silver' && 'bg-gradient-to-r from-gray-300 to-gray-400 text-white',
            achievementInfo.color === 'bronze' && 'bg-gradient-to-r from-orange-400 to-orange-500 text-white',
            achievementInfo.color === 'purple' && 'bg-gradient-to-r from-purple-400 to-purple-500 text-white',
            achievementInfo.color === 'blue' && 'bg-gradient-to-r from-blue-400 to-blue-500 text-white',
            'shadow-sm border border-white/20'
          )}
        >
          <span className="mr-1">{achievementInfo.icon}</span>
          <span className="font-medium">{achievementInfo.name}</span>
        </span>
      )}
    </div>
  );
}