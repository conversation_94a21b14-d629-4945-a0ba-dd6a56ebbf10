import React from 'react';
import { cn } from '@/lib/utils';
import { Calendar, GraduationCap, Award } from 'lucide-react';

interface GraduationYearDisplayProps {
  year: number;
  program?: string;
  honors?: boolean;
  className?: string;
  showYearsSince?: boolean;
}

export function GraduationYearDisplay({
  year,
  program,
  honors,
  className,
  showYearsSince = true,
}: GraduationYearDisplayProps) {
  const currentYear = new Date().getFullYear();
  const yearsSince = currentYear - year;
  
  const getReunionMilestone = () => {
    if (yearsSince === 5) return { text: '5th Year Reunion', color: 'text-green-600' };
    if (yearsSince === 10) return { text: '10th Year Reunion', color: 'text-blue-600' };
    if (yearsSince === 25) return { text: 'Silver Jubilee', color: 'text-gray-600' };
    if (yearsSince === 50) return { text: 'Golden Jubilee', color: 'text-yellow-600' };
    return null;
  };

  const milestone = getReunionMilestone();

  return (
    <div className={cn('flex flex-col space-y-2', className)}>
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-2">
          <GraduationCap className="w-5 h-5 text-muted-foreground" />
          <span className="text-lg font-semibold">
            Class of {year}
          </span>
        </div>
        
        {honors && (
          <div className="flex items-center space-x-1">
            <Award className="w-4 h-4 text-yellow-500" />
            <span className="text-sm text-yellow-600 font-medium">Honors</span>
          </div>
        )}
      </div>

      {program && (
        <div className="text-sm text-muted-foreground">
          {program}
        </div>
      )}

      {showYearsSince && (
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              {yearsSince} {yearsSince === 1 ? 'year' : 'years'} since graduation
            </span>
          </div>
          
          {milestone && (
            <span className={cn('text-sm font-medium', milestone.color)}>
              ✨ {milestone.text}
            </span>
          )}
        </div>
      )}
    </div>
  );
}