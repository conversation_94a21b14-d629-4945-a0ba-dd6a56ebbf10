import React from 'react';
import { cn } from '@/lib/utils';
import { BrandConfig } from '@/lib/branding/brand-config';
import { User, Hash, Building2, Calendar, Shield } from 'lucide-react';

interface StudentIdCardProps {
  studentId: string;
  name: string;
  batch: number;
  school: string;
  program?: string;
  photo?: string;
  verified?: boolean;
  className?: string;
  variant?: 'compact' | 'full';
}

export function StudentIdCard({
  studentId,
  name,
  batch,
  school,
  program,
  photo,
  verified = false,
  className,
  variant = 'compact',
}: StudentIdCardProps) {
  const schoolInfo = BrandConfig.academic.schools.find(s => s.id === school);

  if (variant === 'compact') {
    return (
      <div className={cn(
        'inline-flex items-center space-x-3 px-4 py-2 rounded-lg',
        'bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800',
        'border border-slate-200 dark:border-slate-700',
        className
      )}>
        <Hash className="w-4 h-4 text-muted-foreground" />
        <span className="font-mono text-sm font-semibold">{studentId}</span>
        {verified && (
          <Shield className="w-4 h-4 text-green-500" title="Verified Alumni" />
        )}
      </div>
    );
  }

  return (
    <div className={cn(
      'relative overflow-hidden rounded-xl shadow-lg',
      'bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800',
      'p-6 text-white',
      className
    )}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.4"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          backgroundSize: '60px 60px'
        }} />
      </div>

      {/* Header */}
      <div className="relative flex items-start justify-between mb-4">
        <div>
          <div className="text-xs uppercase tracking-wider opacity-80 mb-1">
            {BrandConfig.institution.name}
          </div>
          <div className="text-lg font-bold">
            Alumni ID Card
          </div>
        </div>
        {verified && (
          <div className="flex items-center space-x-1 bg-green-500 bg-opacity-20 px-2 py-1 rounded-full">
            <Shield className="w-4 h-4" />
            <span className="text-xs font-medium">Verified</span>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="relative space-y-3">
        <div className="flex items-start space-x-4">
          {/* Photo */}
          <div className="w-20 h-24 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
            {photo ? (
              <img src={photo} alt={name} className="w-full h-full object-cover rounded-lg" />
            ) : (
              <User className="w-10 h-10 text-white opacity-50" />
            )}
          </div>

          {/* Details */}
          <div className="flex-1 space-y-2">
            <div>
              <div className="text-xs uppercase tracking-wider opacity-60">Name</div>
              <div className="font-bold text-lg">{name}</div>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <div>
                <div className="text-xs uppercase tracking-wider opacity-60">ID Number</div>
                <div className="font-mono font-semibold">{studentId}</div>
              </div>
              <div>
                <div className="text-xs uppercase tracking-wider opacity-60">Batch</div>
                <div className="font-semibold">{batch}</div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3 pt-2 border-t border-white border-opacity-20">
          <div className="flex items-center space-x-2">
            <Building2 className="w-4 h-4 opacity-60" />
            <div>
              <div className="text-xs opacity-60">School</div>
              <div className="text-sm font-medium">
                {schoolInfo?.icon} {schoolInfo?.name || school}
              </div>
            </div>
          </div>
          
          {program && (
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4 opacity-60" />
              <div>
                <div className="text-xs opacity-60">Program</div>
                <div className="text-sm font-medium">{program}</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="relative mt-4 pt-3 border-t border-white border-opacity-20">
        <div className="flex items-center justify-between">
          <div className="text-xs opacity-60">
            Member since {batch}
          </div>
          <div className="text-xs font-mono opacity-60">
            {BrandConfig.institution.shortName}-{studentId}
          </div>
        </div>
      </div>
    </div>
  );
}