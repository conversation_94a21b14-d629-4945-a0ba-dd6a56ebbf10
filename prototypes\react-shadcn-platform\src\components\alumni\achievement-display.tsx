import React from 'react';
import { cn } from '@/lib/utils';
import { Trophy, Medal, Star, Award, Target, Zap } from 'lucide-react';

interface Achievement {
  id: string;
  title: string;
  description: string;
  date: string;
  type: 'academic' | 'professional' | 'community' | 'sports' | 'research' | 'leadership';
  level?: 'gold' | 'silver' | 'bronze' | 'platinum';
  icon?: React.ReactNode;
  verified?: boolean;
}

interface AchievementDisplayProps {
  achievements: Achievement[];
  variant?: 'grid' | 'list' | 'timeline' | 'compact';
  className?: string;
}

export function AchievementDisplay({
  achievements,
  variant = 'grid',
  className,
}: AchievementDisplayProps) {
  const getTypeIcon = (type: Achievement['type']) => {
    switch (type) {
      case 'academic': return <Trophy className="w-5 h-5" />;
      case 'professional': return <Award className="w-5 h-5" />;
      case 'community': return <Star className="w-5 h-5" />;
      case 'sports': return <Medal className="w-5 h-5" />;
      case 'research': return <Target className="w-5 h-5" />;
      case 'leadership': return <Zap className="w-5 h-5" />;
      default: return <Award className="w-5 h-5" />;
    }
  };

  const getLevelStyle = (level?: Achievement['level']) => {
    switch (level) {
      case 'platinum':
        return 'bg-gradient-to-br from-slate-300 via-slate-400 to-slate-500 text-white shadow-xl';
      case 'gold':
        return 'bg-gradient-to-br from-yellow-400 via-yellow-500 to-amber-600 text-white shadow-lg';
      case 'silver':
        return 'bg-gradient-to-br from-gray-300 via-gray-400 to-gray-500 text-white shadow-lg';
      case 'bronze':
        return 'bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 text-white shadow-md';
      default:
        return 'bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-700 text-white';
    }
  };

  const getTypeBadgeColor = (type: Achievement['type']) => {
    const colors = {
      academic: 'bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300',
      professional: 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300',
      community: 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300',
      sports: 'bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300',
      research: 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300',
      leadership: 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300',
    };
    return colors[type] || colors.professional;
  };

  if (variant === 'compact') {
    return (
      <div className={cn('flex flex-wrap gap-2', className)}>
        {achievements.map((achievement) => (
          <div
            key={achievement.id}
            className={cn(
              'inline-flex items-center space-x-2 px-3 py-1.5 rounded-full',
              getLevelStyle(achievement.level),
              'text-sm font-medium'
            )}
            title={achievement.description}
          >
            {achievement.icon || getTypeIcon(achievement.type)}
            <span>{achievement.title}</span>
            {achievement.verified && (
              <span className="text-xs opacity-75">✓</span>
            )}
          </div>
        ))}
      </div>
    );
  }

  if (variant === 'timeline') {
    return (
      <div className={cn('space-y-4', className)}>
        {achievements.map((achievement, index) => (
          <div key={achievement.id} className="flex space-x-4">
            <div className="relative">
              <div className={cn(
                'w-10 h-10 rounded-full flex items-center justify-center',
                getLevelStyle(achievement.level)
              )}>
                {achievement.icon || getTypeIcon(achievement.type)}
              </div>
              {index < achievements.length - 1 && (
                <div className="absolute top-10 left-5 w-0.5 h-full bg-border" />
              )}
            </div>
            <div className="flex-1 pb-8">
              <div className="flex items-start justify-between mb-1">
                <h3 className="font-semibold">{achievement.title}</h3>
                <span className="text-sm text-muted-foreground">{achievement.date}</span>
              </div>
              <p className="text-sm text-muted-foreground mb-2">{achievement.description}</p>
              <span className={cn(
                'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                getTypeBadgeColor(achievement.type)
              )}>
                {achievement.type}
              </span>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (variant === 'list') {
    return (
      <div className={cn('space-y-3', className)}>
        {achievements.map((achievement) => (
          <div
            key={achievement.id}
            className="flex items-center space-x-4 p-4 rounded-lg bg-card border border-border/40 hover:shadow-md transition-shadow"
          >
            <div className={cn(
              'w-12 h-12 rounded-lg flex items-center justify-center',
              getLevelStyle(achievement.level)
            )}>
              {achievement.icon || getTypeIcon(achievement.type)}
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="font-semibold">{achievement.title}</h3>
                {achievement.verified && (
                  <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full">Verified</span>
                )}
              </div>
              <p className="text-sm text-muted-foreground">{achievement.description}</p>
            </div>
            <div className="text-right">
              <span className={cn(
                'inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium',
                getTypeBadgeColor(achievement.type)
              )}>
                {achievement.type}
              </span>
              <div className="text-sm text-muted-foreground mt-1">{achievement.date}</div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Default: Grid variant
  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4', className)}>
      {achievements.map((achievement) => (
        <div
          key={achievement.id}
          className="relative overflow-hidden rounded-xl border border-border/40 bg-card hover:shadow-lg transition-all duration-300 group"
        >
          {/* Level indicator bar */}
          <div className={cn('h-1', getLevelStyle(achievement.level))} />
          
          <div className="p-5">
            <div className="flex items-start justify-between mb-3">
              <div className={cn(
                'w-12 h-12 rounded-lg flex items-center justify-center',
                getLevelStyle(achievement.level),
                'group-hover:scale-110 transition-transform'
              )}>
                {achievement.icon || getTypeIcon(achievement.type)}
              </div>
              {achievement.verified && (
                <span className="text-xs bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 px-2 py-1 rounded-full font-medium">
                  Verified
                </span>
              )}
            </div>
            
            <h3 className="font-bold text-lg mb-2">{achievement.title}</h3>
            <p className="text-sm text-muted-foreground mb-3">{achievement.description}</p>
            
            <div className="flex items-center justify-between">
              <span className={cn(
                'inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium',
                getTypeBadgeColor(achievement.type)
              )}>
                {achievement.type}
              </span>
              <span className="text-sm text-muted-foreground">{achievement.date}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}