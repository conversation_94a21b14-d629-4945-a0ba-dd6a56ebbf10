// Alumni Platform Branding Configuration
// This file defines the unique branding and identity for the alumni platform

export const BrandConfig = {
  // Institution Identity
  institution: {
    name: 'Global Excellence University',
    shortName: 'GEU',
    tagline: 'Connecting Minds, Building Futures',
    established: 1985,
    type: 'University', // University, College, Institute, Academy
    accreditation: 'NAAC A++',
  },

  // Alumni Network Branding
  alumni: {
    networkName: 'GEU Alumni Network',
    memberTitle: 'Alumni',
    memberTitlePlural: 'Alumni',
    chapters: [
      { id: 'north', name: 'Northern Chapter', region: 'North America' },
      { id: 'south', name: 'Southern Chapter', region: 'South America' },
      { id: 'east', name: 'Eastern Chapter', region: 'Asia Pacific' },
      { id: 'west', name: 'Western Chapter', region: 'Europe & Africa' },
    ],
    batches: {
      format: 'YYYY', // Year format
      prefix: 'Batch of', // e.g., "Batch of 2020"
      special: [
        { year: 1985, name: 'Founding Batch', badge: 'gold' },
        { year: 2010, name: 'Silver Jubilee Batch', badge: 'silver' },
        { year: 2025, name: 'Ruby Anniversary Batch', badge: 'ruby' },
      ],
    },
  },

  // Visual Identity
  visual: {
    logo: {
      primary: '/logo-primary.svg',
      icon: '/logo-icon.svg',
      wordmark: '/logo-wordmark.svg',
    },
    colors: {
      // Brand colors (HSL format for theme compatibility)
      primary: '215 70% 45%', // Deep Blue - Trust & Excellence
      secondary: '35 85% 50%', // Gold - Achievement & Legacy
      accent: '165 60% 40%', // Teal - Growth & Connection
      
      // Semantic colors for alumni features
      achievement: '45 95% 50%', // Golden for achievements
      milestone: '280 70% 50%', // Purple for milestones
      reunion: '350 80% 55%', // Warm red for reunions
      mentorship: '200 70% 45%', // Sky blue for mentorship
    },
    typography: {
      headingFont: 'Playfair Display', // Elegant, academic
      bodyFont: 'Inter', // Clean, modern
      accentFont: 'Bebas Neue', // For special callouts
    },
    patterns: {
      geometric: '/patterns/geometric.svg',
      academic: '/patterns/academic.svg',
      network: '/patterns/network.svg',
    },
  },

  // Academic Structure
  academic: {
    schools: [
      { id: 'engineering', name: 'School of Engineering', icon: '⚙️' },
      { id: 'business', name: 'School of Business', icon: '📊' },
      { id: 'medicine', name: 'School of Medicine', icon: '🏥' },
      { id: 'arts', name: 'School of Liberal Arts', icon: '🎨' },
      { id: 'sciences', name: 'School of Sciences', icon: '🔬' },
    ],
    programs: [
      { id: 'bachelor', name: "Bachelor's Degree", abbr: 'B.', duration: 4 },
      { id: 'master', name: "Master's Degree", abbr: 'M.', duration: 2 },
      { id: 'doctoral', name: 'Doctoral Degree', abbr: 'Ph.D.', duration: 5 },
      { id: 'certificate', name: 'Certificate Program', abbr: 'Cert.', duration: 1 },
    ],
    achievements: [
      { id: 'summa', name: 'Summa Cum Laude', icon: '🏆', color: 'gold' },
      { id: 'magna', name: 'Magna Cum Laude', icon: '🥈', color: 'silver' },
      { id: 'cum', name: 'Cum Laude', icon: '🥉', color: 'bronze' },
      { id: 'dean', name: "Dean's List", icon: '📜', color: 'purple' },
      { id: 'scholar', name: 'Merit Scholar', icon: '🎓', color: 'blue' },
    ],
  },

  // Special Features
  features: {
    reunions: {
      intervals: [5, 10, 25, 50], // Year intervals for reunions
      themes: {
        5: 'Wooden Reunion',
        10: 'Tin Reunion',
        25: 'Silver Jubilee',
        50: 'Golden Jubilee',
      },
    },
    mentorship: {
      levels: [
        { id: 'peer', name: 'Peer Mentor', minYears: 0 },
        { id: 'junior', name: 'Junior Mentor', minYears: 3 },
        { id: 'senior', name: 'Senior Mentor', minYears: 7 },
        { id: 'master', name: 'Master Mentor', minYears: 15 },
      ],
    },
    contributions: {
      types: [
        { id: 'financial', name: 'Financial Support', icon: '💰' },
        { id: 'mentorship', name: 'Mentorship Hours', icon: '👥' },
        { id: 'placement', name: 'Job Placements', icon: '💼' },
        { id: 'guest', name: 'Guest Lectures', icon: '🎤' },
        { id: 'research', name: 'Research Collaboration', icon: '🔬' },
      ],
      recognition: [
        { threshold: 1000, title: 'Contributor', badge: 'bronze' },
        { threshold: 10000, title: 'Patron', badge: 'silver' },
        { threshold: 50000, title: 'Benefactor', badge: 'gold' },
        { threshold: 100000, title: 'Legacy Builder', badge: 'platinum' },
      ],
    },
  },

  // Networking
  networking: {
    connectionTypes: [
      { id: 'batch', name: 'Batchmate', icon: '👥', strength: 'strong' },
      { id: 'school', name: 'Schoolmate', icon: '🏫', strength: 'medium' },
      { id: 'mentor', name: 'Mentor/Mentee', icon: '🤝', strength: 'strong' },
      { id: 'chapter', name: 'Chapter Member', icon: '🌍', strength: 'medium' },
      { id: 'interest', name: 'Interest Group', icon: '💡', strength: 'light' },
    ],
    interestGroups: [
      { id: 'entrepreneurship', name: 'Entrepreneurs Circle', icon: '🚀' },
      { id: 'research', name: 'Research Network', icon: '🔬' },
      { id: 'social', name: 'Social Impact', icon: '🌱' },
      { id: 'tech', name: 'Tech Innovators', icon: '💻' },
      { id: 'arts', name: 'Creative Collective', icon: '🎨' },
    ],
  },

  // Metrics & Analytics
  metrics: {
    engagement: [
      { id: 'posts', name: 'Posts Shared', icon: '📝', unit: 'posts' },
      { id: 'connections', name: 'Connections Made', icon: '🔗', unit: 'connections' },
      { id: 'events', name: 'Events Attended', icon: '📅', unit: 'events' },
      { id: 'mentorship', name: 'Mentorship Hours', icon: '⏰', unit: 'hours' },
      { id: 'referrals', name: 'Job Referrals', icon: '💼', unit: 'referrals' },
    ],
    achievements: [
      { score: 100, title: 'Active Member', badge: '🟢' },
      { score: 500, title: 'Engaged Alumni', badge: '🔵' },
      { score: 1000, title: 'Community Leader', badge: '🟣' },
      { score: 5000, title: 'Alumni Ambassador', badge: '🟡' },
    ],
  },
};

export type BrandConfigType = typeof BrandConfig;